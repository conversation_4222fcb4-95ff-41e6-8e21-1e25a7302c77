{"system": "GeminiGen.AI", "helloWorld": "Hallo Welt!", "Describe the image you want to generate...": "Beschreiben Sie das Bild, das Sie generieren möchten...", "appTitle": "GeminiGen.AI", "copyright": "Copyright © {year}, GeminiGen.AI", "available": "Verfügbar für neue Projekte", "notAvailable": "<PERSON><PERSON> nicht verfügbar", "blog": "Blog", "copyLink": "<PERSON>", "minRead": "MIN LESEN", "articleLinkCopied": "Artikel-Link in die Zwischenablage kopiert", "clickToClose": "Klicken Sie irgendwo oder drücken Sie ESC zum Schließen", "promptDetails": "Prompt-Details", "generateWithPrompt": "<PERSON>t diesem Prompt generieren", "generateWithSettings": "Mit diesen Einstellungen generieren", "preset": "Voreinstellung", "style": "Stil", "resolution": "Auflösung", "addImage": "Bild hinzufügen", "modelPreset": "Modell/Voreinstellung", "imageDimensions": "Bildabmessungen", "yourImage": "Ihr Bild", "generate": "<PERSON><PERSON><PERSON>", "nav.aitool": "KI-Tool", "nav.api": "API", "nav.login": "Anmelden", "nav.history": "Geschichte", "nav.orders": "Bestellungen", "3D Render": "3D-Rendering", "Acrylic": "Acryl", "Anime General": "<PERSON><PERSON> Allgemein", "Creative": "K<PERSON><PERSON>v", "Dynamic": "Dynamisch", "Fashion": "Mode", "Game Concept": "Spielkonzept", "Graphic Design 3D": "3D-Grafikdesign", "Illustration": "Illustration", "None": "<PERSON><PERSON>", "Portrait": "Porträt", "Portrait Cinematic": "Kinematisches Porträt", "Portrait Fashion": "Mode-Porträt", "Ray Traced": "Raytracing", "Stock Photo": "Stockfoto", "Watercolor": "<PERSON><PERSON><PERSON>", "AI Image Generator": "KI-Bildgenerator", "Generate AI images from text prompts with a magical particle transformation effect": "Generieren Sie KI-Bilder aus Text-Prompts mit einem magischen Partikeltransformationseffekt", "Enter your prompt": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Prompt ein", "Generating...": "Generiere...", "Generate Image": "<PERSON><PERSON><PERSON> gene<PERSON>", "Enter a prompt and click Generate Image to create an AI image": "<PERSON><PERSON>en Sie einen Prompt ein und klicken Sie auf Bild generieren, um ein KI-Bild zu erstellen", "Prompt:": "Prompt:", "Download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "How It Works": "Wie es funktioniert", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Dieser KI-Bildgenerator verwendet einen partikelbasierten Transformationseffekt, um den Erstellungsprozess zu visualisieren. Wenn Sie einen Prompt eingeben und auf 'Generieren' klicken, macht das System:", "Sends your prompt to an AI image generation API": "Sendet Ihren Prompt an eine KI-Bildgenerierungs-API", "Creates a particle system with thousands of tiny particles": "Erstellt ein Partikelsystem mit Tausenden von winzigen Partikeln", "Transforms the random noise particles into the generated image": "Transformiert die zufälligen Rauschpartikel in das generierte Bild", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "Die Partikel beginnen in einem zufälligen Rauschmuster und verwandeln sich dann sanft in das endgültige Bild, wodurch ein magischer Effekt entsteht, der den kreativen Prozess der KI simuliert.", "Transform": "Transformieren", "Transforming...": "Transformiere...", "Initializing particles...": "Initialisiere Partikel...", "Loading image...": "Lade Bild...", "Creating particle system...": "<PERSON><PERSON>elle Partikelsystem...", "Adding event listeners...": "Füge Event-Listener hinzu...", "Ready!": "Bereit!", "AI Image Particle Effect": "KI-Bild-Partikeleffekt", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Eine Demonstration der BaseMagicImage-Komponente, die Partikel in KI-generierte Bilder transformiert", "Click anywhere or press ESC to close": "Klicken Sie irgendwo oder drücken Sie ESC zum Schließen", "auth.login": "Anmelden", "auth.loginDescription": "Melden Sie sich in Ihrem Konto an, um fortzufahren", "auth.email": "E-Mail", "auth.enterEmail": "Geben Sie Ihre E-Mail ein", "auth.password": "Passwort", "auth.enterPassword": "Geben Sie Ihr Passwort ein", "auth.rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "auth.welcomeBack": "Willkommen zurück", "auth.signupFailed": "Registrierung fehlgeschlagen", "auth.signupFailedDescription": "<PERSON>s gab einen Fehler bei der Registrierung. Bitte versuchen Sie es erneut.", "auth.dontHaveAccount": "<PERSON>ben <PERSON> kein Konto?", "auth.signUp": "Registrieren", "auth.forgotPassword": "Passwort vergessen?", "auth.bySigningIn": "Durch die Anmeldung stimmen Si<PERSON> unseren", "auth.termsOfService": "Nutzungsbedingungen", "auth.signUpTitle": "Registrieren", "auth.signUpDescription": "<PERSON><PERSON><PERSON><PERSON> ein Konto, um zu beginnen", "auth.name": "Name", "auth.enterName": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Namen ein", "auth.createAccount": "<PERSON><PERSON> er<PERSON>", "auth.alreadyHaveAccount": "Haben <PERSON> bereits ein Konto?", "auth.bySigningUp": "Durch die Registrierung stimmen Si<PERSON> unseren", "auth.backToHome": "Zurück zur Startseite", "auth.notVerifyAccount": "Ihr Konto ist nicht verifiziert. Bitte verifizieren Sie Ihr Konto, um fortzufahren", "auth.verifyAccount": "Konto verifizieren", "auth.resendActivationEmail": "Aktivierungs-E-Mail erneut senden", "auth.accountRecovery": "Konto-Wiederherstellung", "auth.accountRecoveryTitle": "<PERSON><PERSON><PERSON> Konto wieder her", "auth.accountRecoveryDescription": "Geben Sie Ihre E-Mail ein, um Anweisungen zur Passwort-Zurücksetzung zu erhalten", "auth.sendRecoveryEmail": "Wiederherstellungs-E-Mail senden", "auth.recoveryEmailSent": "Wiederherstellungs-E-Mail gesendet", "auth.recoveryEmailSentDescription": "Bitte überprüfen Sie Ihre E-Mail für Anweisungen zur Passwort-Zurücksetzung", "auth.resetPassword": "Passwort zurücksetzen", "auth.resetPasswordTitle": "Setzen Sie Ihr Passwort zurück", "auth.resetPasswordDescription": "<PERSON><PERSON><PERSON> Sie Ihr neues Passwort ein", "auth.newPassword": "Neues Passwort", "auth.confirmPassword": "Passwort bestätigen", "auth.enterNewPassword": "<PERSON><PERSON><PERSON> Sie Ihr neues Passwort ein", "auth.enterConfirmPassword": "Bestätigen Sie Ihr neues Passwort", "auth.passwordResetSuccess": "Passwort-Zurücksetzung erfolgreich", "auth.passwordResetSuccessDescription": "Ihr Passwort wurde erfolgreich zurückgesetzt. Sie können sich jetzt mit Ihrem neuen Passwort anmelden", "auth.activateAccount": "Konto aktivieren", "auth.activateAccountTitle": "Aktivieren Sie Ihr Konto", "auth.activateAccountDescription": "Ihr Konto wird aktiviert...", "auth.accountActivated": "Konto aktiviert", "auth.accountActivatedDescription": "Ihr Konto wurde erfolgreich aktiviert. Si<PERSON> können sich jetzt anmelden", "auth.activationFailed": "Aktivierung fehlgeschlagen", "auth.activationFailedDescription": "Fehler beim Aktivieren Ihres Kontos. Bitte versuchen Sie es erneut oder kontaktieren Sie den Support", "auth.backToLogin": "Zurück zur Anmeldung", "auth.loginFailed": "Anmeldung fehlgeschlagen", "auth.loginWithGoogle": "Mit Google anmelden", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "Ungültige E-Mail", "validation.passwordMinLength": "Das Passwort muss mindestens 8 Zeichen lang sein", "validation.nameRequired": "Name ist erforderlich", "validation.required": "<PERSON><PERSON> ist erford<PERSON>lich", "validation.passwordsDoNotMatch": "Passwörter stimmen nicht überein", "imageSelect.pleaseSelectImageFile": "Bitte wählen Si<PERSON> eine Bilddatei aus", "imageSelect.selectedImage": "Ausgewähltes Bild", "imageSelect.removeImage": "Bild entfernen", "pixelReveal.loading": "Lade Bild...", "pixelReveal.processing": "Verarbeite Bild...", "pixelReveal.revealComplete": "Bildaufdeckung abgeschlossen", "SIGNIN_WRONG_EMAIL_PASSWORD": "Falsche E-Mail oder Passwort", "Try again": "<PERSON><PERSON><PERSON> versuchen", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Generieren Sie hochwertige, detaillierte Bilder mit präziser Textdarstellung für kreative visuelle Inhalte.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Drücken Sie Ihre Ideen aus wie nie zuvor — mit Imagen hat Kreativität keine Grenzen.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash ist ein mächtiges Tool zur Generierung von Bildern aus Text-Prompts.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "<PERSON><PERSON> Ko<PERSON>, Konsistenz und Kreativität als je zuvor.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video trifft Audio. Unser neuestes Videogenerierungsmodell, entwickelt, um Filmemacher und Geschichtenerzähler zu stärken.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "Das fortschrittlichste Text-zu-Sprache-Modell verfügbar.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Großmaßstäbliche Verarbeitung (z.B. mehrere PDFs).\nAufgaben mit niedriger Latenz und hohem Volumen, die Denken erfordern\nAgentische Anwendungsfälle", "aiToolMenu.link": "Link", "aiToolMenu.linkDescription": "Verwenden Sie NuxtLink mit Superkräften.", "aiToolMenu.soon": "Bald", "readArticle": "<PERSON><PERSON><PERSON> lesen", "switchToLightMode": "Zum hellen Modus wechseln", "switchToDarkMode": "Zum dunklen Modus wechseln", "profile": "Profil", "buyCredits.checkout": "<PERSON><PERSON>", "buyCredits.checkoutDescription": "Bestätigen Sie Ihre Bestellung und wählen Sie dann Ihre Zahlungsmethode.", "buyCredits.orderDetail": "Bestelldetails", "buyCredits.credits": "Credits", "buyCredits.pricePerUnit": "Preis pro Einheit", "buyCredits.totalCredits": "Gesamte Credits", "buyCredits.totalPrice": "Gesamtpreis", "buyCredits.payment": "Zahlung", "buyCredits.submit": "<PERSON><PERSON><PERSON><PERSON>", "buyCredits.cancel": "Abbrechen", "pricing.title": "<PERSON><PERSON>", "pricing.description": "Wählen Sie den perfekten Plan für Ihre Bildgenerierungsanforderungen", "pricing.comingSoon": "Demnächst", "pricing.comingSoonDescription": "Unsere Preispläne werden gerade finalisiert. Schauen Sie bald wieder vorbei für Updates.", "magicImageDemo.title": "KI-Bild-Partikeleffekt", "magicImageDemo.description": "Eine Demonstration der BaseMagicImage-Komponente, die Partikel in KI-generierte Bilder transformiert", "magicImageDemo.image": "Bild", "magicImageDemo.aboutTitle": "Über diese Komponente", "magicImageDemo.aboutDescription": "Die BaseMagicImage-Komponente verwendet Three.js, um ein Partikelsystem zu erstellen, das sich zwischen zufälligen Positionen und einem KI-generierten Bild transformieren kann. Die Partikel bewegen sich mit wirbelnden und fließenden Effekten und schaffen eine magische Transformation.", "magicImageDemo.featuresTitle": "Funktionen", "magicImageDemo.features.particleRendering": "Partikelbasiertes Bild-Rendering", "magicImageDemo.features.smoothTransitions": "Sanfte Übergänge zwischen zufälligen Partikelpositionen und Bildformung", "magicImageDemo.features.interactiveControls": "Interaktive Kamerasteuerung (ziehen zum Drehen, scrollen zum Zoomen)", "magicImageDemo.features.customizable": "Anpassbare Partikelanzahl und Animationsdauer", "magicImageDemo.features.automatic": "Automatische oder manuelle Transformationsauslösung", "magicImageDemo.howItWorksTitle": "Wie es funktioniert", "magicImageDemo.howItWorksDescription": "Die Komponente analysiert die Pixel eines Bildes und erstellt ein 3D-Partikelsystem, bei dem jedes Partikel einen Pixel repräsentiert. Hellere Pixel werden näher zum Betrachter positioniert, wodurch ein subtiler 3D-Effekt entsteht. Die Partikel sind zunächst zufällig im 3D-Raum verstreut und animieren sich dann zur Bildung des Bildes, wenn sie ausgelöst werden.", "privacy.title": "Datenschutzerklärung", "privacy.description": "<PERSON><PERSON><PERSON><PERSON>, wie wir Ihre Privatsphäre schützen und Ihre Daten verwalten", "privacy.informationWeCollect": "<PERSON><PERSON>, die wir sammeln", "privacy.informationWeCollectDescription": "Wir sammeln Informationen, die Si<PERSON> uns direkt zur Verfügung stellen, wie wenn Si<PERSON> ein Konto erstellen, unsere Dienste nutzen oder uns für Support kontaktieren.", "privacy.howWeUseInformation": "Wie wir Ihre Informationen verwenden", "privacy.howWeUseInformationDescription": "Wir verwenden die Informationen, die wir sammeln, um unsere Dienste bereitz<PERSON>ellen, zu pflegen und zu verbessern, Transaktionen zu verarbeiten und mit Ihnen zu kommunizieren.", "privacy.informationSharing": "Informationsaustausch", "privacy.informationSharingDescription": "Wir verkaufen, tauschen oder übertragen Ihre persönlichen Informationen nicht an Dritte ohne Ihre Zustimmung, außer wie in dieser Richtlinie beschrieben.", "privacy.dataSecurity": "Datensicherheit", "privacy.dataSecurityDescription": "Wir implementieren angemessene Sicherheitsmaßnahmen, um Ihre persönlichen Informationen vor unbefugtem Zugriff, Änderung, Offenlegung oder Zerstörung zu schützen.", "privacy.contactUs": "Kontaktieren Sie uns", "privacy.contactUsDescription": "<PERSON>n Sie Fragen zu dieser Datenschutzerklärung haben, kontaktieren Sie uns bitte über unsere Support-Kanäle.", "terms.title": "Nutzungsbedingungen", "terms.description": "Bedingungen für die Nutzung der Imagen-Dienste", "terms.acceptanceOfTerms": "1. <PERSON><PERSON><PERSON>", "terms.acceptanceOfTermsDescription": "Durch den Zugriff auf und die Nutzung der Imagen-Dienste akzeptieren und stimmen Si<PERSON> zu, an die Bedingungen und Bestimmungen dieser Vereinbarung gebunden zu sein.", "terms.useOfService": "2. Nutzung des Dienstes", "terms.useOfServiceDescription": "<PERSON>e stimmen zu, unseren Dienst nur für rechtmäßige Zwecke und in Übereinstimmung mit diesen Nutzungsbedingungen zu verwenden.", "terms.userAccounts": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "terms.userAccountsDescription": "Sie sind dafür verantwo<PERSON>, die Vertraulichkeit Ihres Kontos und Passworts zu wahren.", "terms.intellectualProperty": "4. <PERSON><PERSON><PERSON><PERSON><PERSON>", "terms.intellectualPropertyDescription": "Alle Inhalte und Materialien, die auf unserem Dienst verfügbar sind, sind durch Rechte an geistigem Eigentum geschützt.", "terms.termination": "5. <PERSON><PERSON><PERSON><PERSON><PERSON>", "terms.terminationDescription": "Wir können Ihr Konto und den Zugang zum Dienst nach unserem alleinigen Ermessen kündigen oder aussetzen.", "terms.disclaimers": "6. Haftungsauss<PERSON><PERSON><PERSON>sse", "terms.disclaimersDescription": "Der Dienst wird 'wie er ist' ohne jegliche Garantien bereitgestellt.", "terms.contactUsTerms": "Kontaktieren Sie uns", "terms.contactUsTermsDescription": "Wenn Sie Fragen zu diesen Nutzungsbedingungen haben, kontaktieren Sie uns bitte über unsere Support-Kanäle.", "Describe the video you want to generate...": "Beschreiben Sie das Video, das Si<PERSON> erstellen möchten...", "cancel": "Stornieren", "confirm": "Bestätigen", "appName": "GeminiGen.AI", "quickTopUp": "Schnelles Aufladen", "customTopUp": "Benutzerdefinierte Aufladung", "numberOfCredits": "<PERSON><PERSON><PERSON>", "paypal": "PayPal", "paypalDescription": "<PERSON><PERSON> mit Ihrem Pay<PERSON>al-<PERSON><PERSON> bezahlen", "debitCreditCard": "Debit- oder Kreditkarte", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "<PERSON><PERSON> be<PERSON>en", "cryptoDescription": "Bitcoin, Ethereum und andere Kryptowährungen", "profileMenu.guide": "Führung", "profileMenu.logo": "Logo", "profileMenu.settings": "Einstellungen", "profileMenu.components": "Komponenten", "loadingMoreItems": "Weitere Artikel werden geladen...", "promptLabel": "Aufforderung:", "videoExamples": "Videobeispiele", "videoExamplesDescription": "Erkunden Sie diese Video-Beispiele mit ihren Eingaben und Einstellungen. Klicken Sie auf eine beliebige Schaltfläche 'Diesen Prompt verwenden', um den Prompt in Ihr Eingabefeld zu kopieren.", "useThisPrompt": "<PERSON><PERSON><PERSON><PERSON> diesen Hinweis", "model": "<PERSON><PERSON>", "duration": "<PERSON><PERSON>", "videoTypeSelection": "Videotyp auswählen", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Videoerstellung ausstehend", "notifications.types.video_1.description": "Die Videogenerierung wartet auf die Verarbeitung.", "notifications.types.video_2.title": "Videoerstellung abgeschlossen", "notifications.types.video_2.description": "Video wurde erfolgreich erstellt.", "notifications.types.video_3.title": "Videoerstellung fehlgeschlagen", "notifications.types.video_3.description": "Videogenerierung fehlgeschlagen", "notifications.types.image_1.title": "Bildgenerierung ausstehend", "notifications.types.image_1.description": "Die Bilderzeugung wartet auf die Bearbeitung.", "notifications.types.image_2.title": "Bilderzeugung abgeschlossen", "notifications.types.image_2.description": "Das Bild wurde erfolgreich generiert.", "notifications.types.image_3.title": "Bilderstellung fehlgeschlagen", "notifications.types.image_3.description": "Bildgenerierung fehlgeschlagen", "notifications.types.tts_history_1.title": "Audioerzeugung ausstehend", "notifications.types.tts_history_1.description": "Text-to-Speech wartet darauf, verarbeitet zu werden.", "notifications.types.tts_history_2.title": "Audiogenerierung abgeschlossen", "notifications.types.tts_history_2.description": "Die Text-zu-Sprache-Audio wurde erfolgreich generiert.", "notifications.types.tts_history_3.title": "Audiogenerierung fehlgeschlagen", "notifications.types.tts_history_3.description": "Die Text-zu-Sprache-Generierung ist fehlgeschlagen.", "notifications.types.voice_training_1.title": "Sprachtraining ausstehend", "notifications.types.voice_training_1.description": "Stimmtraining wartet auf die Bearbeitung.", "notifications.types.voice_training_2.title": "Sprachtraining abgeschlossen", "notifications.types.voice_training_2.description": "Das Training des benutzerdefinierten Sprachmodells wurde erfolgreich abgeschlossen.", "notifications.types.voice_training_3.title": "Stimmtraining fehlgeschlagen", "notifications.types.voice_training_3.description": "Stimmtraining fehlgeschlagen", "notifications.types.music_1.title": "Musikgeneration ausstehend", "notifications.types.music_1.description": "Musikerzeugung wartet darauf, verarbeitet zu werden.", "notifications.types.music_2.title": "Musikerzeugung abgeschlossen", "notifications.types.music_2.description": "KI-Musik wurde erfolgreich generiert.", "notifications.types.music_3.title": "Musikerstellung fehlgeschlagen", "notifications.types.music_3.description": "Musikerstellung fehlgeschlagen", "notifications.types.speech_1.title": "Spracherzeugung ausstehend", "notifications.types.speech_1.description": "Ihre Anfrage zur Spracherzeugung wartet auf die Verarbeitung.", "notifications.types.speech_2.title": "Spracherzeugung abgeschlossen", "notifications.types.speech_2.description": "Ihre Rede wurde erfolgreich erstellt.", "notifications.types.speech_3.title": "Sprachgenerierung fehlgeschlagen", "notifications.types.speech_3.description": "Ihre Spracherstellung ist fehlgeschlagen. Bitte versuchen Sie es erneut.", "notifications.time.justNow": "Gerade eben", "notifications.time.minutesAgo": "Vor {minutes} Minuten", "notifications.time.hoursAgo": "Vor {hours} Stunden", "notifications.time.yesterday": "Gestern", "notifications.status.processing.title": "Verarbeitung", "notifications.status.processing.description": "<PERSON>hre Anfrage wird bearbeitet.", "notifications.status.success.title": "Abgeschlossen", "notifications.status.success.description": "Erfolgreich abgeschlossen", "notifications.status.failed.title": "Fehlgeschlagen", "notifications.status.failed.description": "Ein Fehler ist bei der Verarbeitung aufgetreten.", "notifications.status.warning.title": "<PERSON><PERSON><PERSON>", "notifications.status.warning.description": "Abgeschlossen mit Warnungen", "notifications.status.pending.title": "<PERSON><PERSON><PERSON><PERSON>", "notifications.status.pending.description": "<PERSON>ten auf die Bearbeitung", "notifications.status.cancelled.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications.status.cancelled.description": "<PERSON><PERSON><PERSON> wurde storniert", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Einstellungen", "userMenu.profile": "Profil", "userMenu.buyCredits": "<PERSON> kaufen", "userMenu.settings": "Einstellungen", "userMenu.api": "API", "userMenu.logout": "Abmelden", "userMenu.greeting": "<PERSON><PERSON>, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Stereo", "options.allow": "Erlauben", "options.dontAllow": "<PERSON>cht erlauben", "options.voices": "Stimmen", "options.pickVoice": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "voiceTypes.systemVoices": "Systemstimmen", "voiceTypes.customVoices": "Benutzerdefinierte Stimmen", "voiceTypes.premiumVoices": "Premium-Stimmen", "voiceTypes.userVoices": "Benutzerstimmen", "common.home": "<PERSON>uh<PERSON><PERSON>", "Describe the speech you want to generate...": "Beschreiben Sie die Rede, die Si<PERSON> generieren möchten...", "listenToSpeech": "Rede anhören", "generateSimilar": "Ähnliches erzeugen", "voice": "Stimme", "emotion": "Emotion", "speed": "Geschwindigkeit", "speed_settings": "Geschwindigkeitseinstellungen", "speed_value": "Geschwindigkeitswert", "speed_slider": "Geschwindigkeitsregler", "apply": "<PERSON><PERSON><PERSON>", "speech_settings": "Spracheinstellungen", "current_speed": "Aktuelle Geschwindigkeit", "reset_defaults": "Auf Standardeinstellungen zurücksetzen", "outputFormat": "Ausgabeformat", "outputChannel": "Ausgabekanal", "selectVoice": "<PERSON><PERSON><PERSON> au<PERSON>wählen", "selectEmotion": "Emotion auswählen", "selectFormat": "Format auswählen", "selectChannel": "Kanal auswählen", "noVoicesAvailable": "<PERSON><PERSON>", "noEmotionsAvailable": "<PERSON><PERSON> Em<PERSON>en verfügbar", "searchVoices": "Stimmen suchen...", "searchEmotions": "Emotionssuche...", "noVoicesFound": "<PERSON><PERSON> gefunden", "noEmotionsFound": "<PERSON><PERSON> Em<PERSON>en gefunden", "retry": "<PERSON><PERSON><PERSON> versuchen", "noAudioSample": "Kein Audio-Beispiel verfügbar", "Speech Generation Complete": "Spracherzeugung abgeschlossen", "Your speech has been generated successfully": "Ihre Rede wurde erfolgreich erstellt.", "stripe": "Streifen", "stripeDescription": "<PERSON><PERSON> mit Stripe bezahlen", "history.tabs.imagen": "Bild", "history.tabs.video": "Video", "history.tabs.speech": "Rede", "history.tabs.music": "Mu<PERSON>", "history.tabs.history": "Geschichte", "orders.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.description": "Zeigen Sie Ihre Transaktions- und Zahlungshistorie an.", "orders.orderId": "Bestell-ID", "orders.amount": "Betrag", "orders.credits": "<PERSON><PERSON><PERSON><PERSON>", "orders.quantity": "<PERSON><PERSON>", "orders.platform": "Plattform", "orders.externalId": "Transaktions-ID", "orders.status.completed": "Abgeschlossen", "orders.status.success": "Erfolg", "orders.status.paid": "Be<PERSON>hlt", "orders.status.pending": "<PERSON><PERSON><PERSON><PERSON>", "orders.status.processing": "Verarbeitung", "orders.status.failed": "Fehlgeschlagen", "orders.status.cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.status.error": "<PERSON><PERSON>", "orders.empty.title": "Noch keine Bestellungen.", "orders.empty.description": "Sie haben noch keine Bestellungen aufgegeben. Kaufen Sie Guthaben, um unsere Dienste zu nutzen.", "orders.empty.action": "<PERSON><PERSON><PERSON><PERSON> kaufen", "orders.endOfList": "Sie haben alle Bestellungen g<PERSON>hen.", "orders.errors.fetchFailed": "Fehler beim Laden der Bestellhistorie. Bitte versuchen Sie es erneut.", "orders.meta.title": "Bestellverlauf - Imagen AI", "orders.meta.description": "<PERSON><PERSON> sich Ihren Transaktions- und Zahlungsverlauf auf Imagen AI an", "historyPages.imagenDescription": "Durchsuchen Sie Ihre KI-generierten Bilder und Kunstwerke", "historyPages.musicDescription": "Durchsuchen Sie Ihre KI-generierten Musik- und Audioinhalte", "historyPages.speechDescription": "Durchsuchen Sie Ihre KI-generierten Sprach- und Stimm-Inhalte.", "historyPages.videoDescription": "Durchsuchen Sie Ihre KI-generierten Videos und Animationen", "historyPages.imagenBreadcrumb": "Bild", "historyPages.musicBreadcrumb": "Mu<PERSON>", "historyPages.speechBreadcrumb": "Rede", "historyPages.videoBreadcrumb": "Videoproduktion", "historyPages.endOfImagesHistory": "Sie haben das Ende der Bildhistorie erreicht.", "historyPages.endOfMusicHistory": "Du hast das Ende der Musikgeschichte erreicht.", "historyPages.endOfSpeechHistory": "Sie haben das Ende der Redegeschichte erreicht.", "historyPages.endOfVideoHistory": "Sie haben das Ende der Videohistorie erreicht.", "historyPages.noVideosFound": "Keine Videos gefunden", "historyPages.noVideosFoundDescription": "Beginnen Sie mit der Erstellung von Videos, um sie hier zu sehen.", "historyPages.backToLibrary": "Zurück zur Bibliothek", "historyPages.errorLoadingVideo": "Fehler beim Laden des Videos", "historyPages.loadingVideoDetails": "Lade Videodetails...", "historyPages.videoDetails": "Videodetails", "historyPages.videoInformation": "Videoinformationen", "historyPages.videoNotFound": "Das gewünschte Video konnte nicht gefunden oder geladen werden.", "historyPages.aiContentLibraryTitle": "AI-Inhaltsbibliothek", "historyPages.aiContentLibraryDescription": "Durchsuchen und verwalten Sie Ihre KI-generierten Inhalte in verschiedenen Kategorien.", "demo.notifications.title": "Benachrichtigungstypen & Statusdemo", "demo.notifications.description": "Beispiele für verschiedene Benachrichtigungstypen mit unterschiedlichen Statuszuständen", "demo.notifications.statusLegend": "<PERSON><PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.title": "Sprachauswahl-Demo", "demo.speechVoiceSelect.description": "Vorführung der wiederverwendbaren BaseSpeechVoiceSelectModal-Komponente mit modelValue-Props", "aspectRatio": "Seitenverhältnis", "Image Reference": "Bildreferenz", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "Sicherheitsfilterstufe", "used_credit": "Gebrauchter Kredit", "Safety Filter": "Sicherheitsfilter", "safetyFilter.blockLowAndAbove": "Block niedrig und darüber", "safetyFilter.blockMediumAndAbove": "Block Mittel und darüber", "safetyFilter.blockOnlyHigh": "Nur Hoch blockieren", "safetyFilter.blockNone": "Block None", "historyFilter.all": "Alle", "historyFilter.imagen": "Bild", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Sprachgen", "Person Generation": "Personengenerierung", "downloadImage": "Bild her<PERSON><PERSON><PERSON>n", "noImageAvailable": "<PERSON><PERSON>", "enhancePrompt": "Aufforderung verbessern", "addImages": "Bilder <PERSON><PERSON><PERSON><PERSON><PERSON>", "generateVideo": "Video erstellen", "happy": "<PERSON><PERSON><PERSON><PERSON>", "sad": "<PERSON><PERSON><PERSON><PERSON>", "angry": "<PERSON><PERSON><PERSON>", "excited": "Aufger<PERSON><PERSON>", "laughing": "<PERSON><PERSON>", "crying": "<PERSON><PERSON>", "calm": "<PERSON><PERSON><PERSON>", "serious": "<PERSON>", "frustrated": "<PERSON><PERSON><PERSON><PERSON>", "hopeful": "Hoffnungsvoll", "narrative": "Erzählung", "kids' storytelling": "Geschichten für Kinder", "audiobook": "<PERSON><PERSON><PERSON><PERSON>", "poetic": "Poetisch", "mysterious": "Geheimnisvoll", "inspirational": "Inspirierend", "surprised": "Überrascht", "confident": "Selbstbewusst", "romantic": "<PERSON><PERSON><PERSON>", "scared": "Verängstigt", "trailer voice": "Trailerstimme", "advertising": "Werbung", "documentary": "Dokumentarfilm", "newsreader": "<PERSON><PERSON><PERSON>tensprecher", "weather report": "Wetterbericht", "game commentary": "Spielkommentar", "interactive": "Interaktiv", "customer support": "Kundensupport", "playful": "Verspielt", "tired": "<PERSON><PERSON><PERSON>", "sarcastic": "Sarkastisch", "disgusted": "<PERSON><PERSON><PERSON><PERSON>", "whispering": "Flüstern", "persuasive": "Überzeugend", "nostalgic": "Nostalgisch", "meditative": "Meditativ", "announcement": "Ankündigung", "professional pitch": "Professionelle Präsentation", "casual": "Lässig", "exciting trailer": "<PERSON><PERSON><PERSON> Trailer", "dramatic": "Dramatisch", "corporate": "Unternehmen", "tech enthusiast": "Tech-Enthusiast", "youthful": "<PERSON><PERSON><PERSON><PERSON>", "calming reassurance": "Beruhigende Rückversicherung", "heroic": "<PERSON><PERSON>", "festive": "<PERSON><PERSON>", "urgent": "Dringend", "motivational": "Motivierend", "friendly": "<PERSON><PERSON><PERSON><PERSON>", "energetic": "Energiegeladen", "serene": "<PERSON><PERSON><PERSON>", "bold": "<PERSON><PERSON>", "charming": "<PERSON><PERSON><PERSON>", "monotone": "Monoton", "questioning": "Infragestellung", "directive": "Rich<PERSON><PERSON><PERSON>", "dreamy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "epic": "<PERSON><PERSON><PERSON>", "lyrical": "Lyrisch", "mystical": "Mystisch", "melancholy": "Melan<PERSON><PERSON>", "cheerful": "<PERSON><PERSON><PERSON><PERSON>", "eerie": "<PERSON><PERSON><PERSON>", "flirtatious": "<PERSON><PERSON><PERSON><PERSON>", "thoughtful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cinematic": "Filmisch", "humorous": "Humorvoll", "instructional": "Instruktiv", "conversational": "Konversationell", "apologetic": "Entschuldigend", "excuse-making": "Ausredenmachen", "encouraging": "Ermutigend", "neutral": "Neutral", "authoritative": "Autoritativ", "sarcastic cheerful": "Sarkastisch Fröhlich", "reassuring": "Beruhigend", "formal": "Formal", "anguished": "Gequält", "giggling": "<PERSON><PERSON><PERSON>", "exaggerated": "Übertrieben", "cold": "<PERSON><PERSON>", "hot-tempered": "Hitzköpfig", "grateful": "Dankbar", "regretful": "<PERSON><PERSON><PERSON><PERSON>", "provocative": "Provokativ", "triumphant": "Triumphierend", "vengeful": "Ra<PERSON><PERSON><PERSON><PERSON>", "heroic narration": "Heroische Erzählung", "villainous": "Schurkenhaft", "hypnotic": "Hypnotisch", "desperate": "Verzweifelt", "lamenting": "Klagen", "celebratory": "<PERSON><PERSON><PERSON>", "teasing": "Necken", "exhausted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questioning suspicious": "Verdächtiges infrage stellen", "optimistic": "Optimistisch", "bright, gentle voice, expressing excitement.": "<PERSON><PERSON>, sanfte <PERSON>im<PERSON>, die Aufregung ausdrückt.", "low, slow voice, conveying deep emotions.": "Tief, lang<PERSON>im<PERSON>, die tiefe Emotionen vermittelt.", "sharp, exaggerated voice, expressing frustration.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Stimme, die Frustration ausdrückt.", "fast, lively voice, full of enthusiasm.": "<PERSON><PERSON><PERSON>, le<PERSON><PERSON><PERSON>im<PERSON>, voller Begeisterung.", "interrupted, joyful voice, interspersed with laughter.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, fre<PERSON><PERSON>im<PERSON>, durch<PERSON><PERSON>t mit Lachen.", "shaky, low voice, expressing pain.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, die Schmerz ausdrückt.", "gentle, steady voice, providing reassurance.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Stimme, die Zuversicht vermittelt.", "mature, clear voice, suitable for formal content.": "<PERSON><PERSON>, klare <PERSON>, geeignet für formelle Inhalte.", "weary, slightly irritated voice.": "<PERSON><PERSON><PERSON>, leicht verärgerte Stimme.", "bright voice, conveying positivity and hope.": "<PERSON><PERSON> Stimme, die Positivität und Hoffnung vermittelt.", "natural, gentle voice with a slow rhythm.": "<PERSON><PERSON><PERSON><PERSON>, sanfte Stimme mit langsamem Rhythmus.", "lively, engaging voice, captivating for children.": "<PERSON><PERSON><PERSON><PERSON>, fess<PERSON><PERSON> Stimme, die Kinder begeistert.", "even, slow voice, emphasizing content meaning.": "<PERSON><PERSON><PERSON><PERSON> ruhige, lang<PERSON>, die den Inhalt betont.", "rhythmic, emotional voice, conveying subtlety.": "Rhythmische, emotionale Stimme, die Feinheit vermittelt.", "low, slow voice, evoking curiosity.": "<PERSON><PERSON><PERSON>, la<PERSON><PERSON>, die Neugier weckt.", "strong, passionate voice, driving action.": "<PERSON><PERSON>, leidenschaftliche Stimme, treibende Aktion.", "high, interrupted voice, expressing astonishment.": "<PERSON><PERSON>, unterbrochene Stimme, die Erstaunen ausdrückt.", "firm, powerful voice, persuasive and assuring.": "Feste, kraftvolle Stimme, überzeugend und beruhigend.", "sweet, gentle voice, suitable for emotional content.": "<PERSON><PERSON><PERSON>, sanfte Stimme, geeignet für emotionale Inhalte.", "shaky, interrupted voice, conveying anxiety.": "<PERSON><PERSON><PERSON><PERSON>, unterbro<PERSON><PERSON> Stimme, die Angst vermittelt.", "deep, strong voice with emphasis, creating suspense.": "<PERSON><PERSON><PERSON>, starke Stimme mit Betonung, Spannung erzeugend.", "engaging, lively voice, emphasizing product benefits.": "<PERSON><PERSON><PERSON>, leben<PERSON><PERSON> Stimme, die die Produktvorteile hervorhebt.", "formal, clear voice with focus on key points.": "<PERSON><PERSON>, klare Stimme mit Fokus auf die wichtigsten Punkte.", "calm, profound voice, delivering authenticity.": "<PERSON><PERSON><PERSON>, tie<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Stimme, die Authentizität vermittelt.", "standard, neutral voice, clear and precise.": "Standard, neutrale Stimme, klar und präzise.", "bright, neutral voice, suitable for concise updates.": "<PERSON><PERSON>, <PERSON><PERSON>, geeignet für prägnante Updates.", "fast, lively voice, stimulating excitement.": "<PERSON><PERSON><PERSON>, le<PERSON><PERSON><PERSON> Stimme, anregende Aufregung.", "friendly, approachable voice, encouraging engagement.": "<PERSON><PERSON><PERSON><PERSON>, zugäng<PERSON> Stimme, die zur Beteiligung ermutigt.", "empathetic, gentle voice, easy to connect with.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sanfte Stimme, leicht zugä<PERSON>lich.", "clear voice, emphasizing questions and answers.": "<PERSON><PERSON><PERSON>, die Fragen und Antworten betont.", "cheerful, playful voice with a hint of mischief.": "<PERSON><PERSON><PERSON><PERSON>, verspielte Stimme mit einem Hauch von Unfug.", "slow, soft voice lacking energy.": "Langsame, sanfte Stimme ohne Energie.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON>, scharfe <PERSON>, manchmal humorvoll.", "cold voice, clearly expressing discomfort.": "<PERSON><PERSON><PERSON><PERSON>, die deutliches Unbehagen ausdrückt.", "soft, mysterious voice, creating intimacy.": "<PERSON><PERSON><PERSON>, geheimnisvolle Stimme, die Intimität schafft.", "emotional voice, convincing the listener to act.": "Emotionale Stimme, die den Zuhörer zum Handeln auffordert.", "gentle voice, evoking feelings of reminiscence.": "<PERSON><PERSON><PERSON> Stimme, die Gefühle der Erinnerung hervorruft.", "even, relaxing voice, suitable for mindfulness.": "Selbst ruhige Stimme, geeignet für Achtsamkeit.", "clear voice, emphasizing key words.": "<PERSON><PERSON><PERSON>, Betonung der Schlüsselwörter.", "confident, clear voice, ideal for business presentations.": "<PERSON><PERSON><PERSON><PERSON><PERSON>wuss<PERSON>, klar<PERSON>im<PERSON>, ideal für Geschäftspräsentationen.", "natural, friendly voice, as if talking to a friend.": "<PERSON><PERSON><PERSON><PERSON>, fre<PERSON><PERSON>im<PERSON>, als ob man mit einem Freund sp<PERSON>t.", "fast, powerful voice, creating tension and excitement.": "<PERSON><PERSON><PERSON>, kraftvolle Stimme, die Spannung und Aufregung erzeugt.", "emphasized, suspenseful voice, creating intensity.": "<PERSON><PERSON>, spannungsgel<PERSON><PERSON> Stimme, die Intensität erzeugt.", "professional, formal voice, suitable for business content.": "Professionelle, formelle Sprache, geeignet für Geschäftsinhalte.", "energetic, lively voice, introducing new technologies.": "Energiegel<PERSON><PERSON>, le<PERSON><PERSON><PERSON> Stimme, die neue Technologien vorstellt.", "vibrant, cheerful voice, appealing to younger audiences.": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, die für jüngeres Publikum ansprechend ist.", "gentle, empathetic voice, easing concerns.": "<PERSON><PERSON><PERSON>, ein<PERSON><PERSON><PERSON><PERSON>imme, die Sorgen lindert.", "strong, decisive voice, full of inspiration.": "<PERSON><PERSON>, en<PERSON><PERSON><PERSON><PERSON>, voller Inspiration.", "bright, excited voice, suitable for celebrations.": "<PERSON><PERSON>, begei<PERSON><PERSON> Stimme, geeignet für Feierlichkeiten.", "fast, strong voice, emphasizing urgency.": "<PERSON><PERSON><PERSON>, starke <PERSON>, die Dringlichkeit betont.", "passionate, inspiring voice, encouraging action.": "Leidenschaftliche, inspirierende Stimme, die zum Handeln ermutigt.", "warm, approachable voice, fostering connection.": "<PERSON><PERSON>, zug<PERSON>ng<PERSON> Stimme, die Verbindung fördert.", "fast, powerful voice, brimming with enthusiasm.": "<PERSON><PERSON><PERSON>, kraft<PERSON><PERSON> Stimme, voller Enthusiasmus.", "slow, gentle voice, evoking peace and tranquility.": "<PERSON><PERSON>, sanfte Stimme, die Frieden und Ruhe heraufbeschwört.", "firm, assertive voice, exuding confidence.": "<PERSON><PERSON>, bestim<PERSON><PERSON> Stimme, die Selbstbewusstsein ausstrahlt.", "warm, captivating voice, leaving a strong impression.": "<PERSON><PERSON>, fess<PERSON>nde Stimme, die einen starken Eindruck hinterlässt.", "flat, unvaried voice, conveying neutrality or irony.": "<PERSON><PERSON><PERSON>, einheitliche Stimme, die Neutralität oder Ironie vermittelt.", "curious voice, emphasizing questions.": "<PERSON>eu<PERSON><PERSON><PERSON>, die Fragen betont.", "firm, clear voice, guiding the listener step-by-step.": "<PERSON><PERSON>, klare <PERSON>, die den Zuhörer Schritt für Schritt anleitet.", "gentle, slow voice, evoking a floating sensation.": "<PERSON><PERSON><PERSON>, lang<PERSON>, die ein schwebendes Gefühl hervorruft.", "deep, resonant voice, emphasizing grandeur.": "<PERSON><PERSON><PERSON>, resonante Stimme, die Größe betont.", "soft, melodic voice, similar to singing.": "<PERSON><PERSON><PERSON>, melodische Stimme, <PERSON><PERSON><PERSON> wie Singen.", "low, drawn-out voice, evoking mystery.": "<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> Stimme, die Geheimnisvolles hervorruft.", "slow, low voice, conveying deep sadness.": "<PERSON><PERSON>, tiefe <PERSON>, die tiefe Traurigkeit vermittelt.", "bright, energetic voice, full of positivity.": "<PERSON><PERSON>, energetische Stimme, voller Positivität.", "low, whispery voice, evoking fear or strangeness.": "<PERSON><PERSON>, fl<PERSON><PERSON><PERSON> Stimme, die Angst oder Fremdheit hervorruft.", "sweet, teasing voice, full of allure.": "<PERSON><PERSON><PERSON>, neckende Stimme, voller Anziehungskraft.", "slow, reflective voice, full of contemplation.": "Langsame, nachdenkliche Stimme, voller Überlegung.", "resonant, emphasized voice, creating a movie-like effect.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, die einen filmähnlichen Effekt erzeugt.", "lighthearted, cheerful voice, sometimes exaggerated.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, fr<PERSON><PERSON><PERSON><PERSON> Ton, manchmal übertrieben.", "clear, slow voice, guiding the listener step-by-step.": "<PERSON><PERSON><PERSON>, la<PERSON><PERSON>, die den Zuhörer Schritt für Schritt anleitet.", "natural voice, as if chatting with the listener.": "<PERSON><PERSON><PERSON><PERSON>, als ob man sich mit dem Zuhörer unterhält.", "soft, sincere voice, expressing regret.": "<PERSON><PERSON><PERSON>, auf<PERSON><PERSON><PERSON> Stimme, die Bedauern ausdrückt.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON><PERSON><PERSON><PERSON>, unsichere Stimme, manch<PERSON> unbeholfen.", "warm voice, providing motivation and support.": "Warme Stimme, bietet Motivation und Unterstützung.", "even voice, free of emotional bias.": "Gleichm<PERSON><PERSON><PERSON> Stimme, fre<PERSON> von emotionaler Voreingenommenheit.", "strong, powerful voice, exuding credibility.": "<PERSON><PERSON>, kraftvolle Stimme, strahlt Glaubwürdigkeit aus.", "cheerful voice with an undertone of mockery.": "Fr<PERSON><PERSON><PERSON> Stimme mit eine<PERSON> Un<PERSON><PERSON> von <PERSON>.", "gentle, empathetic voice, providing comfort.": "<PERSON><PERSON><PERSON>, ein<PERSON><PERSON><PERSON><PERSON>im<PERSON>, die Trost spendet.", "clear, polite voice, suited for formal occasions.": "<PERSON><PERSON><PERSON>, h<PERSON><PERSON><PERSON> Stimme, geeignet für formelle Anlässe.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON>, zitte<PERSON><PERSON> Stimme, die Not ausdrückt.", "interrupted voice, mixed with light laughter.": "Unterbrochene Stimme, gemischt mit leichtem Lachen.", "loud, emphasized voice, often humorous.": "<PERSON><PERSON>, bet<PERSON>, oft humorvoll.", "flat, unemotional voice, conveying detachment.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, die Distanz vermittelt.", "fast, sharp voice, sometimes out of control.": "<PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON>, manch<PERSON> außer <PERSON>.", "warm, sincere voice, expressing appreciation.": "<PERSON><PERSON>, auf<PERSON><PERSON><PERSON> Stimme, die Wertschätzung ausdrückt.", "low, subdued voice, full of remorse.": "<PERSON><PERSON>, gedä<PERSON><PERSON><PERSON> Stimme, voller <PERSON>ue.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, voller Anspielung.", "loud, powerful voice, full of victory.": "<PERSON><PERSON>, kraftvolle Stimme, voller Sieg.", "low, cold voice, expressing determination for revenge.": "<PERSON><PERSON><PERSON>, kalt<PERSON>, die Entschlossenheit zur Rache ausdrückt.", "strong, inspiring voice, emphasizing heroic deeds.": "<PERSON><PERSON>, inspi<PERSON><PERSON><PERSON>, die heldenhafte Taten betont.", "low, drawn-out voice, full of scheming.": "<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> Stimme, voller Intrigen.", "even, repetitive voice, drawing the listener in.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, monotone Stimme, die den Zuhörer in ihren Bann zieht.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, die Hoffnungslosigkeit ausdrückt.", "low, sorrowful voice, as if mourning.": "<PERSON><PERSON>, t<PERSON><PERSON><PERSON>, als ob sie trauert.", "excited, joyful voice, full of festive spirit.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, f<PERSON><PERSON><PERSON>, voller festlicher Stimmung.", "light, playful voice, sometimes mockingly.": "<PERSON><PERSON><PERSON>, verspielt<PERSON> Stimme, manch<PERSON> spö<PERSON>sch.", "weak, broken voice, expressing extreme fatigue.": "<PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON> Stimme, die extreme Erschöpfung ausdrückt.", "slow, emphasized voice, full of suspicion.": "<PERSON><PERSON>, <PERSON><PERSON>, vol<PERSON>.", "bright, hopeful voice, creating positivity.": "<PERSON><PERSON>, hoffnungsvolle Stimme, die Positivität schafft.", "Your audio will be processed with the latest stable model.": "Ihr Audio wird mit dem neuesten stabilen Modell verarbeitet.", "Your audio will be processed with the latest beta model.": "Ihr Audio wird mit dem neuesten Beta-Modell verarbeitet.", "You don't have any saved prompts yet.": "Du hast noch keine gespeicherten Eingabeaufforderungen.", "Commercial Use": "Gewerbliche Nutzung", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "Sie haben das Recht, die durch unsere Dienste erzeugte Sprachausgabe für persönliche, Bildungs- oder kommerzielle Zwecke zu nutzen. Sie dürfen jedoch die Sprachausgabe nicht ohne vorherige schriftliche Zustimmung von Text To Speech OpenAI weiterverkaufen, weiterverteilen oder unterlizenzieren.", "Other people’s privacy": "Die Privatsphäre anderer Menschen", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "<PERSON>e müssen die Privatsphäre anderer respektieren, wenn Sie unsere Dienste nutzen. Laden Si<PERSON> keine Sprachinhalte hoch oder erzeugen Si<PERSON> keine, die persönliche Informationen, vertrauliche Daten oder urheberrechtlich geschütztes Material ohne Erlaubnis enthalten.", "{price}$ per credit": "{preis} $ pro Kredit", "Pricing": "Preisgestaltung", "Simple and flexible. Only pay for what you use.": "Ein<PERSON>ch und flexibel. Zahlen Sie nur für das, was <PERSON><PERSON> nutzen.", "Pay as you go": "Bezahlen nach Nutzung", "Flexible": "Flexibel", "Input characters": "Eingabezeichen", "Audio model": "Audiomodell", "Credits": "Gutschriften", "Cost": "<PERSON><PERSON>", "HD quality voices": "HD-Qualitätsstimmen", "Advanced model": "Erweitertes Modell", "Buy now": "Jetzt kaufen", "Paste your text to calculate": "Fügen Sie Ihren Text ein, um zu berechnen.", "Paste your text here...": "<PERSON>ügen Sie hier Ihren Text ein...", "Calculate": "<PERSON><PERSON><PERSON><PERSON>", "Estimate your cost by drag the slider below or": "Schätzen Sie Ihre Kosten, indem Sie den Schieberegler unten bewegen oder", "calming": "Beruhigend", "customer": "Kunde", "exciting": "aufregend", "excuse": "Entschuldigung", "game": "Spiel", "hot": "<PERSON><PERSON><PERSON>", "kids": "Kinder", "professional": "Professionell", "tech": "Technik", "trailer": "<PERSON><PERSON><PERSON><PERSON>", "weather": "Wetter", "No thumbnail available": "<PERSON><PERSON>", "Debit or Credit Card": "Debit- oder Kreditkarte", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express und mehr", "Top up now": "Jetzt aufladen", "noVideoAvailable": "<PERSON>in <PERSON> verfügbar", "common.back": "Zurück", "common.edit": "<PERSON><PERSON><PERSON>", "common.save": "Speichern", "common.cancel": "Abbrechen", "common.delete": "Löschen", "common.copy": "<PERSON><PERSON><PERSON>", "common.copied": "<PERSON><PERSON><PERSON>", "common.manage": "<PERSON><PERSON><PERSON><PERSON>", "aiToolMenu.textToImage": "Text zu Bild", "profileMenu.integration": "Integration", "videoTypes.examples.tikTokDanceTrend": "TikTok-Tanztrend", "videoTypes.examples.energeticDanceDescription": "Energisches Tanzvideo mit lebhaften Farben, sch<PERSON><PERSON>n, <PERSON><PERSON>, vertikalem Format, im Stil sozialer Medien", "videoTypes.examples.instagramReel": "Instagram Reel", "videoTypes.examples.lifestyleDescription": "Lifestyle-Inhalte mit ästhetischen Bildern, sanften Übergängen, trendigen Effekten, fesselndem Storytelling", "videoTypes.examples.comedySketch": "Comedy-Sketch", "videoTypes.examples.funnyComedyDescription": "Lustige Komödieszene mit ausdrucksstarken Charakteren, humorvollen Situationen, unterhaltsamen Dialogen, heiter<PERSON>im<PERSON>.", "videoTypes.examples.productLaunchAd": "Werbeanzeige für Produkteinführung", "videoTypes.examples.professionalCorporateDescription": "Professionelles Unternehmensvideo mit Führungskräftepräsentation, sauberem Büroambiente, Geschäftsstil in formeller Kleidung", "videoTypes.examples.quickPromoVideo": "Schnelles Werbevideo", "videoTypes.examples.fastPacedPromoDescription": "Schnelllebiger Promotionsinhalt mit effizienter Produktion, kostengünstigen visuellen Elementen und optimierter Botschaft.", "videoTypes.examples.birthdayGreeting": "Geburtstagsgruß", "videoTypes.examples.personalizedBirthdayDescription": "Personalisierte Geburtstagsvideo mit festlichen Dekorationen, warm<PERSON>, feierlicher Atmosphäre, herzlicher Botschaft", "videoTypes.examples.brandStoryVideo": "Markengeschichtenvideo", "videoTypes.examples.tutorialVideo": "Anleitungsvideo", "videoTypes.examples.manOnThePhone": "Mann am Telefon", "videoTypes.examples.runningSnowLeopard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videoTypes.examples.snowLeopard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videoTypes.styles.cartoonAnimated": "Zeichentrick- oder Animationsstilvideo", "videoTypes.styles.naturalDocumentary": "Dokumentarische Aufnahmen im natürlichen Stil", "videoTypes.styles.naturalLifelike": "<PERSON><PERSON><PERSON><PERSON>r, lebensechter Videostil", "videoTypes.styles.professionalMovieQuality": "Professionelle filmähnliche Qualität mit dramatischer Beleuchtung", "videoTypes.styles.creativeStylized": "Kreative und stilisierte Videoeffekte", "videoTypes.styles.retroVintage": "Retro- oder Vintage-Videoästhetik", "historyFilter.dialogueGen": "Dialog-Gen", "historyFilter.speechGenDocument": "Sprachausgabe aus Dokument", "demo.notifications.availableNotificationTypes": "Verfügbare Benachrichtigungstypen", "demo.speechVoiceSelect.example1": "Beispiel 1: Standardverwendung", "demo.speechVoiceSelect.example2": "Beispiel 2: Kleine Größe", "demo.speechVoiceSelect.example3": "Beispiel 3: Großes Format", "demo.speechVoiceSelect.example4": "Beispiel 4: <PERSON><PERSON><PERSON> Fehlerbeispiele", "demo.speechVoiceSelect.example5": "Beispiel 5: <PERSON><PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.mainNarrator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.characterVoice": "Charakterstimme", "demo.speechVoiceSelect.selectedVoicesSummary": "Ausgewählte Stimmen Zusammenfassung:", "demo.speechVoiceSelect.clearAll": "Alle löschen", "demo.speechVoiceSelect.setRandomVoices": "Zufällige Stimmen festlegen", "demo.speechVoiceSelect.logToConsole": "In Konsole protokollieren", "demo.speechVoiceSelect.notSelected": "Nicht ausgewählt", "demo.speechVoiceSelect.voiceSelectionsChanged": "Sprachauswahl geändert", "demo.historyWrapper.title": "Demo des Statusabzeichens von HistoryWrapper", "demo.historyWrapper.normalStatus": "Beispiel 1: Normaler Status (Status = 1)", "demo.historyWrapper.processingStatus": "Beispiel 2: Verarbeitungsstatus (Status = 2)", "demo.historyWrapper.errorStatus": "Beispiel 3: <PERSON><PERSON><PERSON><PERSON> (Status = 3) - <PERSON><PERSON><PERSON>", "demo.historyWrapper.multipleErrorExamples": "Beispiel 4: <PERSON><PERSON><PERSON> Fehlerbeispiele", "demo.historyWrapper.statusComparison": "Beispiel 5: <PERSON><PERSON><PERSON><PERSON>", "demo.historyWrapper.normalImageGeneration": "Normale Bilderzeugung", "demo.historyWrapper.videoGenerationInProgress": "Videogenerierung im Gange", "demo.historyWrapper.speechGenerationFailed": "Spracherzeugung fehlgeschlagen", "demo.historyWrapper.imageFailed": "Bild fehlgeschlagen", "demo.historyWrapper.videoFailed": "Video fehlgeschlagen", "demo.historyWrapper.speechFailed": "Ansprache fehlgeschlagen", "demo.historyWrapper.statusSuccess": "Status: Erfolg", "demo.historyWrapper.statusProcessing": "Status: In Bearbeitung", "demo.historyWrapper.statusError": "Status: <PERSON><PERSON>", "demo.historyWrapper.status1Success": "Status 1: Erfolg", "demo.historyWrapper.status2Processing": "Status 2: Verarbeitung", "demo.historyWrapper.badgeBehavior": "Abzeichenverhalten:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Zeigt nur Typ- und Stilabzeichen", "demo.historyWrapper.showsTypeStyleAndError": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> und rotes Fehlerabzeichen mit Warnsymbol an.", "demo.historyWrapper.redBackgroundWithWhite": "<PERSON><PERSON>grund mit weißem Text und Warndreieck-Symbol", "demo.historyWrapper.allBadgesHideOnHover": "Alle Abzeichen verbergen sich bei Hover, um Overlay-Inhalte anzuzeigen.", "demo.speechVoiceCaching.title": "S<PERSON>chs<PERSON>mmen-Caching-Test", "demo.speechVoiceCaching.description": "Test zur Überprüfung der Zwischenspeicherung von Stimmen zwischen verschiedenen Komponenten.", "demo.speechVoiceCaching.component1Modal": "Komponente 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Komponente 3 - Reguläre Auswahl", "demo.speechVoiceCaching.forceReloadVoices": "Stimmen neu laden erzwingen", "demo.speechVoiceCaching.clearAllSelections": "Alle Auswahlen löschen", "demo.speechVoiceCaching.logStoreState": "Protokollspeicherst<PERSON>", "demo.speechVoiceCaching.refreshPageInstructions": "Aktualisieren Sie die Seite und öffnen Sie eine beliebige Komponente - sie wird neu geladen.", "demo.speechVoiceCaching.checkNetworkTab": "Überprüfen Sie den Netzwerk-Tab, um die API-Aufrufe zu bestätigen.", "demo.speechVoiceCaching.selectedVoicePersist": "Ausgewählte Stimme wird über localStorage gespeichert.", "demo.speechVoiceCaching.pageMounted": "<PERSON><PERSON> mon<PERSON><PERSON>, der Speicher wird bei Bedarf automatisch initialisiert.", "integration.title": "Integration", "integration.subtitle": "Verwalten Sie Ihre API-Schlüssel und Integrationseinstellungen", "integration.apiKeys": "API-Schlüssel", "integration.apiKeysDescription": "Verwalten Sie Ihre API-Schlüssel für den programmgesteuerten Zugriff.", "integration.webhook": "Webhook", "integration.webhookDescription": "Webhook-URL für Benachrichtigungen konfigurieren", "apiKeys.title": "API-Schlüssel", "apiKeys.subtitle": "Verwalten Sie Ihre API-Schlüssel für den programmgesteuerten Zugriff", "apiKeys.create": "API-Schlüssel erstellen", "apiKeys.createNew": "Neuen API-Schlüssel erstellen", "apiKeys.createFirst": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON> erstellen", "apiKeys.name": "Name", "apiKeys.nameDescription": "Geben Sie Ihrem API-Schlüssel einen aussagekräftigen Namen.", "apiKeys.namePlaceholder": "z. B. Mein App-API-Schlüssel", "apiKeys.nameRequired": "Der API-Schlüsselname ist erforderlich.", "apiKeys.createdAt": "<PERSON><PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Keine API-Schlüssel", "apiKeys.noKeysDescription": "Erstellen Sie Ihren ersten API-Schlüssel, um mit dem programmgesteuerten Zugriff zu beginnen.", "apiKeys.created": "API-Schlüssel erfolgreich erstellt", "apiKeys.createError": "Fehler beim Erstellen des API-Schlüssels", "apiKeys.deleted": "API-Schlüssel erfolgreich gelöscht", "apiKeys.deleteError": "Fehler beim Löschen des API-Schlüssels.", "apiKeys.deleteConfirm": "API-Schlüssel löschen", "apiKeys.deleteWarning": "Sind <PERSON> sic<PERSON>, dass Sie diesen API-Schlüssel löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "apiKeys.copied": "API-Schlüssel in die Zwischenablage kopiert", "apiKeys.copyError": "Kopieren des API-Schlüssels fehlgeschlagen", "webhook.title": "Webhook-Konfiguration", "webhook.subtitle": "Konfigurieren Sie die Webhook-URL für Echtzeitbenachrichtigungen.", "webhook.configuration": "Webhook-URL", "webhook.currentUrl": "Aktuelle Webhook-URL", "webhook.currentUrlDescription": "Diese URL wird POST-Anfragen für Webhook-Ereignisse erhalten.", "webhook.notConfigured": "<PERSON><PERSON>-URL konfiguriert", "webhook.url": "Webhook-URL", "webhook.urlDescription": "<PERSON>eb<PERSON> Sie die URL ein, an der Sie Webhook-Benachrichtigungen erhalten möchten.", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "<PERSON>te geben Si<PERSON> zu<PERSON>t eine Webhook-URL ein.", "webhook.invalidUrl": "Bitte geben Si<PERSON> eine gültige URL ein.", "webhook.saved": "Webhook-URL erfolgreich gespeichert", "webhook.saveError": "Fehler beim Speichern der Webhook-URL", "webhook.test": "Test", "webhook.testSent": "Test gesendet", "webhook.testDescription": "Webhook-Test erfolgreich gesendet", "webhook.information": "Webhook-Informationen", "webhook.howItWorks": "Wie es funktioniert", "webhook.description": "<PERSON><PERSON> k<PERSON>, senden wir HTTP-POST-Anfragen an Ihre Webhook-URL, wann immer bestimmte Ereignisse in Ihrem Konto auftreten.", "webhook.events": "Webhook-Ereignisse", "webhook.imageGenerated": "Bilderstellung abgeschlossen", "webhook.imageGenerationFailed": "Bilderzeugung fehlgeschlagen", "webhook.creditUpdated": "<PERSON><PERSON><PERSON><PERSON>", "webhook.payloadFormat": "Nutzlastformat", "webhook.payloadDescription": "Webhook-Anfragen werden als JSON mit der folgenden Struktur gesendet:", "webhook.security": "Sicherheit", "webhook.securityDescription": "Wir empfehlen die Verwendung von HTTPS-URLs und die Implementierung der Signaturüberprüfung, um die Authentizität von Webhooks sicherzustellen.", "error.general": "<PERSON><PERSON>", "error.validation": "Validierungsfehler", "error.required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success.saved": "Erfolgreich gespeichert", "success.created": "Erfolgreich erstellt", "success.deleted": "Erfolg<PERSON><PERSON>", "success.copied": "In die Zwischenablage kopiert", "confirmDelete": "Löschen bestätigen", "confirmDeleteDescription": "Sind <PERSON> sic<PERSON>, dass Sie dieses Element löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "historyDeleted": "Historieneintrag erfolgreich <PERSON>", "deleteError": "Löschen des Verlaufsartikels fehlgeschlagen", "Regenerate Image": "Bild regenerieren", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "Sie haben keine Änderungen an den Einstellungen vorgenommen. Sind <PERSON> sic<PERSON>, dass Sie das gleiche Bild neu generieren möchten?", "Yes, Regenerate": "<PERSON><PERSON>, regene<PERSON><PERSON>.", "Cancel": "Abbrechen", "models.imagen4Fast": "Bild 4 Schnell", "models.imagen4Ultra": "Bild 4 Ultra", "voiceTypes.favoriteVoices": "Lieblingsstimmen", "voiceTypes.geminiVoices": "Gemini-Stimmen", "speech.dialogueGeneration.complete": "Dialogerzeugung abgeschlossen", "speech.dialogueGeneration.failed": "Fehler bei der Dialogerstellung", "speech.dialogueGeneration.pending": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Dialogen ausstehend", "speech.dialogueGeneration.dialogueGen": "Dialog Gen", "speech.dialogueGeneration.successMessage": "Ihr Dialog wurde erfolgreich erstellt.", "speech.speechGeneration.complete": "Sprachgenerierung abgeschlossen", "speech.speechGeneration.failed": "Spracherzeugung fehlgeschlagen", "speech.speechGeneration.pending": "Sprachgenerierung ausstehend", "speech.speechGeneration.successMessage": "Ihre Rede wurde erfolgreich erstellt.", "speech.speechGeneration.requestWaiting": "Ihre Anfrage zur Spracherzeugung wartet auf die Bearbeitung.", "speech.errors.failedToLoadEmotions": "Laden der Emotionen fehlgeschlagen", "tts-document": "<PERSON><PERSON>", "assignVoicesToSpeakers": "St<PERSON>men den Sprechern zuweisen", "speakers": "Lautsprecher", "addSpeaker": "<PERSON><PERSON> hinzufügen", "noVoiceAssigned": "<PERSON><PERSON>", "noSpeakersAdded": "Noch keine Sprecher hinzugefügt.", "assignVoiceToSpeaker": "Stimme {<PERSON><PERSON><PERSON><PERSON>} <PERSON><PERSON><PERSON>", "assigned": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assign": "<PERSON><PERSON><PERSON><PERSON>", "editSpeaker": "<PERSON><PERSON> bearbeiten", "speakerName": "Sprechername", "enterSpeakerName": "Namen des Sprechers eingeben", "save": "Speichern", "speaker": "Lautsprecher", "assignVoices": "<PERSON><PERSON><PERSON>", "speakersWithVoices": "{zugewiesen}/{gesamt} Sprecher haben Stimmen", "dialogs": "Dialoge", "addDialog": "<PERSON><PERSON> hinz<PERSON>", "enterDialogText": "Geben Sie Dialogtext ein...", "selectSpeaker": "<PERSON>ähle Sprecher aus", "generateDialogSpeech": "Dialogrede erzeugen", "voice 1": "Stimme 1", "voice 2": "Stimme 2", "uuid": "UUID", "output_format": "Ausgabeformat", "output_channel": "Ausgabekanal", "file_name": "Dateiname", "file_size": "Dateigröße", "speakers_count": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_prompt": "Benutzerdefinierte Eingabeaufforderung", "Please wait a moment...": "Bitte warten Si<PERSON> einen Moment...", "Click to copy": "Klicken zum Kopieren", "Copied to clipboard": "In die Zwischenablage kopiert", "UUID has been copied to clipboard": "UUID wurde in die Zwischenablage kopiert.", "Credits: {credits} remaining": "Guthaben: {credits} verbleibend", "This generation will cost: {cost} Credits": "Diese Generation wird {cost} Credits kosten.", "Your generated video will appear here": "Ihr generiertes Video wird hier angezeigt.", "Regenerate Video": "Video regenerieren", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "Sie haben keine Änderungen an den Einstellungen vorgenommen. Möchten Sie das gleiche Video wirklich neu generieren?", "Your generated speech will appear here": "Ihre generierte Rede wird hier angezeigt.", "Regenerate Speech": "Sprache regenerieren", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "Sie haben keine Änderungen an den Einstellungen vorgenommen. Sind <PERSON> sic<PERSON>, dass Sie die gleiche Ansprache erneut generieren möchten?", "Generated Speech": "Erzeugte Rede", "Generating speech...": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "View Details": "Details anzeigen", "Speech Examples": "Beispiele für Reden", "Click on any example to use its prompt for speech generation": "Klicken Sie auf ein beliebiges Beispiel, um dessen Aufforderung zur Spracherzeugung zu verwenden.", "Click to use": "<PERSON><PERSON><PERSON>, um zu verwenden", "videoStyles.selectVideoStyle": "Videostil auswählen", "videoStyles.cinematic": "Filmisch", "videoStyles.realistic": "Realistisch", "videoStyles.animated": "<PERSON><PERSON><PERSON><PERSON>", "videoStyles.artistic": "Künstlerisch", "videoStyles.documentary": "Dokumentarfilm", "videoStyles.vintage": "Antik", "ui.buttons.downloadApp": "App herunterladen", "ui.buttons.signUp": "Registrieren", "ui.buttons.viewDetails": "Details anzeigen", "ui.buttons.seeLater": "<PERSON><PERSON><PERSON><PERSON>", "ui.buttons.selectFile": "<PERSON>i ausw<PERSON>hlen", "ui.buttons.selectFiles": "<PERSON><PERSON> au<PERSON>wählen", "ui.buttons.pickAVoice": "<PERSON><PERSON><PERSON>e eine Stimme", "ui.buttons.topUpNow": "Jetzt aufladen", "ui.buttons.pressEscToClose": "Drücken Sie ESC, um zu schließen.", "ui.labels.clickToCopy": "Zum Kopieren klicken", "ui.labels.copiedToClipboard": "In die Zwischenablage kopiert", "ui.labels.noAudioAvailable": "Kein Audio verfügbar", "ui.labels.noThumbnailAvailable": "<PERSON><PERSON> ve<PERSON>", "ui.labels.noPromptAvailable": "<PERSON><PERSON> Eingabeaufforderung verfügbar", "ui.labels.videoModel": "Videomodell", "ui.labels.speechModel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui.labels.generatedSpeech": "Generierte Rede", "ui.labels.defaultVoice": "Standardstimme", "ui.labels.selectAnyVoice": "<PERSON>ählen Si<PERSON> eine beliebige Stimme aus", "ui.labels.cameraMotion": "Kamerabewegung", "ui.labels.transform": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ui.labels.transforming": "Umwandeln...", "ui.messages.imageLoaded": "Bild geladen", "ui.messages.imageRevealComplete": "Bildenthüllung abgeschlossen", "ui.messages.processingImage": "Bild wird verarbeitet", "ui.messages.videoLoaded": "Video geladen", "ui.messages.videoProcessing": "Videobearbeitung", "ui.messages.invalidDownloadLink": "Ungültiger Download-Link", "ui.messages.pleaseSelectSupportedFile": "Bitte wählen Si<PERSON> eine unterstützte Datei aus.", "ui.messages.deleteConfirm": "Löschen bestätigen", "ui.messages.deleteFailed": "Löschen fehlgeschlagen", "ui.messages.youHaveNewNotification": "Sie haben eine neue Benachrichtigung.", "ui.messages.yourGenerationIsReady": "Deine Generation ist bereit.", "ui.errors.errorLoadingImage": "Fe<PERSON> beim Laden des Bildes:", "ui.errors.failedToCopy": "Kopieren fehlgeschlagen:", "ui.errors.failedToPlayAudioPreview": "Fehler beim Abspielen der Audiovorschau:", "ui.errors.wavesurferError": "<PERSON>surfer<PERSON><PERSON><PERSON>:", "ui.errors.somethingWentWrong": "Etwas ist schiefgelaufen. Bitte versuchen Sie es erneut.", "ui.errors.supabaseUrlRequired": "Supabase-URL und anonymer Schlüssel sind erforderlich.", "dialog.startTypingHere": "Beginnen Sie hier mit der Eingabe des Dialogs...", "payment.debitCreditCard": "Debit- oder Kreditkarte", "payment.cardDescription": "Visa, Mastercard, American Express und mehr", "Style Description": "Stilbeschreibung", "Dialog Content": "Dialoginhalt", "Your generated dialog will appear here": "Ihr generierter Dialog wird hier angezeigt.", "Regenerate Dialog": "Dialog regenerieren", "Generated Dialog": "Erzeugter Dialog", "Generating dialog...": "Dialog erzeugen...", "Dialog Information": "Dialoginformation", "Audio Player": "Audioplayer", "Voices": "Stimmen", "Voice 1": "Stimme 1", "Voice 2": "Stimme 2", "Dialog Examples": "Dialogbeispiele", "Click on any example to use its style or dialog content": "Klicken Sie auf ein beliebiges Beispiel, um dessen Stil oder Dialoginhalt zu verwenden.", "Use Style": "Stil verwenden", "Use Dialog": "<PERSON><PERSON> verwenden", "personGeneration": "Personengenerierung", "Imagen": "Bild", "On": "An", "Off": "Aus", "Prompts will always be refined to improve output quality": "Eingabeaufforderungen werden immer verfeinert, um die Ausgabequalität zu verbessern.", "Prompts will not be modified": "Eingaben werden nicht geändert.", "Tips": "<PERSON><PERSON><PERSON>", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Ihr Video wird weiterhin im Hintergrund erstellt. <PERSON><PERSON> können diese Seite schließen und die Registerkarte „Verlauf“ für das erstellte Video prüfen. Wir benachrichtigen Sie, wenn es fertig ist.", "Go to History": "Zur Geschichte gehen", "footer.youtube": "YouTube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Text-zu-Sprache OpenAI", "footer.privacyPolicy": "Datenschutzrichtlinie", "footer.termsOfService": "Nutzungsbedingungen", "footer.terms": "Bedingungen", "footer.privacy": "Privatsphäre", "Generate": "Erzeugen", "Prompt": "Aufforderung", "Generate Video": "Video erstellen", "ui.errors.generationFailed": "Generierung fehlgeschlagen", "downloadVideo": "Video herunterladen", "imageStyles.selectImageStyle": "Bildstil auswählen", "imageStyles.none.description": "<PERSON><PERSON> s<PERSON>scher Stil angewendet", "imageStyles.3d-render.description": "Bild in 3D rendern", "imageStyles.acrylic.description": "Bild im Acrylmalstil erstellen", "imageStyles.anime-general.description": "Bild im Anime-Stil erstellen", "imageStyles.creative.description": "Kreative künstlerische Effekte anwenden", "imageStyles.dynamic.description": "<PERSON><PERSON><PERSON>n Sie dynamische und energetische Visuals", "imageStyles.fashion.description": "Stilbild für Modefotografie", "imageStyles.game-concept.description": "Designbild für Spielekonzeptkunst", "imageStyles.graphic-design-3d.description": "3D-Grafikdesignelemente anwenden", "imageStyles.illustration.description": "<PERSON><PERSON>elle Illustrationskunstwerke", "imageStyles.portrait.description": "Für Porträtfotografie optimieren", "imageStyles.portrait-cinematic.description": "<PERSON><PERSON><PERSON> ein filmisches Porträt im Stil", "imageStyles.portrait-fashion.description": "Mode-Porträt-Styling anwenden", "imageStyles.ray-traced.description": "Mit Raytracing-Effekten rendern", "imageStyles.stock-photo.description": "Erstellen Sie professionelle Stockfoto-Stil", "imageStyles.watercolor.description": "Aquarellmalerei-Effekte anwenden", "imageStyles.examples": "<PERSON><PERSON><PERSON><PERSON>", "ui.messages.dragDropOrClick": "<PERSON><PERSON> hierher ziehen oder klicken, um auszuwählen", "ui.messages.dropFilesHere": "<PERSON><PERSON> hier <PERSON>gen", "ui.messages.selectMultipleFiles": "<PERSON>e können mehrere Dateien auswählen.", "ui.messages.selectSingleFile": "<PERSON><PERSON><PERSON>en Si<PERSON> eine Datei zum Hochladen aus", "ui.messages.supportedFormats": "Unterstützte Formate", "ui.messages.releaseToUpload": "Freigeben zum Hochladen", "ui.labels.generatedAudio": "Erzeugtes Audio", "ui.actions.showResult": "Ergebnis anzeigen", "ui.actions.hideResult": "Ergeb<PERSON> ausblenden", "ui.messages.speechGenerating": "Sprachgenerierung...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Ihre Rede wird immer noch im Hintergrund erstellt. <PERSON><PERSON> können diese Seite schließen und den Verlauf-Tab für das generierte Audio überprüfen, und wir benachrichtigen Sie, wenn es fertig ist.", "downloadAudio": "Audio herunterladen", "All Countries": "Alle Länder", "All Genders": "Alle Geschlechter", "Country": "Land", "Gender": "Geschlecht", "Reset": "Z<PERSON>ücksetzen", "Search by name or description...": "Suche nach Name oder Beschreibung...", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Female": "<PERSON><PERSON><PERSON>", "American": "Amerikaner", "British": "Britisch", "Australian": "Australis<PERSON>", "Indian": "Indisch", "Chinese": "<PERSON><PERSON><PERSON>", "Spanish": "Spanisch", "Canadian": "Kanadisch", "Irish": "<PERSON><PERSON>", "Singaporean": "Singapurer", "Russian": "<PERSON><PERSON>", "German": "De<PERSON>ch", "Portuguese": "Portugiesisch", "Hindi": "Hindi", "Mexican": "Mexikanisch", "Latin American": "Lateinamerikanisch", "Argentine": "A<PERSON><PERSON><PERSON>", "Peninsular": "<PERSON><PERSON><PERSON>", "French": "Franzö<PERSON><PERSON>", "Parisian": "<PERSON><PERSON>", "Standard": "Standard", "Brazilian": "Brasilianisch", "Turkish": "Türkisch", "Istanbul": "Istanbul", "Bavarian": "Bayerisch", "Polish": "Polnisch", "Italian": "Italienisch", "South African": "Südafrikaner", "Scottish": "<PERSON><PERSON><PERSON><PERSON>", "Welsh": "Walisisch", "New Zealand": "Neuseeland", "Dutch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Belgian": "<PERSON><PERSON><PERSON>", "Swedish": "Schwedisch", "Norwegian": "<PERSON><PERSON><PERSON><PERSON>", "Danish": "D<PERSON><PERSON><PERSON>", "Korean": "Koreanisch", "Korean, Seoul": "Koreanisch, Seoul", "Japanese": "Japanisch", "Croatian": "<PERSON><PERSON><PERSON><PERSON>", "Czech": "Tschechisch", "Moravian": "<PERSON><PERSON><PERSON><PERSON>", "Zealandic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Indonesian": "Indonesisch", "Javanese": "Javanisch", "Romanian": "Rumänisch", "Swiss": "Schweizerisch", "Vietnamese": "Vietnamesisch", "Arabic": "Arabisch", "Bulgarian": "Bulgarisch", "Finnish": "Finnisch", "Greek": "Griechisch", "Hungarian": "Ungarisch", "Filipino": "Philippinisch", "History": "Geschichte", "imagen-flash": "Gemini 2.0 Blitz", "Detail": "Detail", "Delete": "Löschen", "ui.errors.unknownError": "Ein unbekannter Fehler ist aufgetreten.", "ui.errors.tryAgainLater": "Bitte versuchen Sie es später noch einmal.", "More": "<PERSON><PERSON>", "tts-text": "Audio", "tts-multi-speaker": "Audio", "tts-history": "Audio", "tts-history_1": "Audio", "tts-history_2": "Audio", "tts-history_3": "Audio", "voice-training": "Stimmtraining", "voice-training_1": "Stimmschulung", "voice-training_2": "Stimmbildung", "Start writing or paste your text here or select a file to generate speech...": "Beginnen Sie mit dem Schreiben oder fügen Sie Ihren Text hier ein oder wählen Sie eine Datei aus, um Sprache zu generieren...", "Selecting a voice...": "Stimme auswählen...", "Voices Library": "Stimmenbibliothek", "Select a voice for your speaker from the library.": "<PERSON>ählen Sie eine Stimme für Ihren Lautsprecher aus der Bibliothek aus.", "Next": "<PERSON><PERSON>", "Back": "Zurück", "Done": "<PERSON><PERSON><PERSON><PERSON>", "I got it!": "Ich habe es!", "historyPages.endOfHistory": "Sie haben das Ende der Geschichte erreicht.", "Press ESC to close": "Drücken Sie ESC, um zu schließen."}